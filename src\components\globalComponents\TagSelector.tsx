'use client';

import { useState } from 'react';
import { X, Plus } from 'lucide-react';
import { Button } from '@heroui/react';
import { tagSelectorData } from './tagSelectorData';

export default function TagSelector() {
  const [tags, setTags] = useState<string[]>(tagSelectorData.initialTags);
  const [selectedTags, setSelectedTags] = useState<string[]>(
    tagSelectorData.selectedTags
  );
  const [showInput, setShowInput] = useState(false);
  const [newTag, setNewTag] = useState('');

  const toggleTag = (tag: string) => {
    if (selectedTags.includes(tag)) {
      setSelectedTags(selectedTags.filter(t => t !== tag));
    } else {
      setSelectedTags([...selectedTags, tag]);
    }
  };

  const handleAddTag = () => {
    const trimmed = newTag.trim();
    if (trimmed && !tags.includes(trimmed)) {
      setTags([...tags, trimmed]);
      setSelectedTags([...selectedTags, trimmed]);
      setNewTag('');
      setShowInput(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddTag();
    }
  };
  const handleAddTag = () => {
    const trimmed = newTag.trim();
    if (trimmed && !tags.includes(trimmed)) {
      setTags([...tags, trimmed]);
      setSelectedTags([...selectedTags, trimmed]);
      setNewTag('');
      setShowInput(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddTag();
    }
  };

  return (
    <div className="rounded-2xl bg-white p-6 w-full max-w-xl space-y-4">
      {/* Input field on top */}
      {showInput && (
        <input
          type="text"
          value={newTag}
          onChange={e => setNewTag(e.target.value)}
          onKeyDown={handleKeyDown}
          placeholder="Enter new tag"
          className="border border-primary-200 rounded-full px-3 py-1.5 text-sm w-3/4 truncate focus:outline-none focus:ring-2 focus:ring-primary-200"
        />
      )}

      {/* Tags + Button Row */}
      <div className="flex justify-between items-center flex-wrap gap-4">
        {/* Tags List */}
        <div className="flex flex-wrap gap-3 flex-1 min-w-0">
          {tags.map(tag => {
            const isSelected = selectedTags.includes(tag);
            return (
              <div
                key={tag}
                onClick={() => toggleTag(tag)}
                onKeyDown={e => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    toggleTag(tag);
                  }
                }}
                role="button"
                tabIndex={0}
                className={`flex flex-row items-center gap-1 border border-primary-200 px-3 py-1.5 rounded-md text-sm cursor-pointer transition max-w-full truncate ${
                  isSelected
                    ? 'bg-primary-200/10 text-primary-200'
                    : 'text-primary-200 hover:bg-purple-50'
                }`}
              >
                <span className="truncate max-w-full -mt-1 font-semibold">
                  {tag}
                </span>
                {isSelected && (
                  <button
                    type="button"
                    onClick={e => {
                      e.stopPropagation();
                      toggleTag(tag);
                    }}
                  >
                    <X className="h-3.5 w-3.5 text-primary-200 shrink-0" />
                  </button>
                )}
              </div>
            );
          })}
        </div>
    <div className="rounded-2xl bg-white p-6 w-full max-w-xl space-y-4">
      {/* Input field on top */}
      {showInput && (
        <input
          type="text"
          value={newTag}
          onChange={e => setNewTag(e.target.value)}
          onKeyDown={handleKeyDown}
          placeholder="Enter new tag"
          className="border border-primary-200 rounded-full px-3 py-1.5 text-sm w-3/4 truncate focus:outline-none focus:ring-2 focus:ring-primary-200"
        />
      )}

      {/* Tags + Button Row */}
      <div className="flex justify-between items-center flex-wrap gap-4">
        {/* Tags List */}
        <div className="flex flex-wrap gap-3 flex-1 min-w-0">
          {tags.map(tag => {
            const isSelected = selectedTags.includes(tag);
            return (
              <div
                key={tag}
                onClick={() => toggleTag(tag)}
                onKeyDown={e => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    toggleTag(tag);
                  }
                }}
                role="button"
                tabIndex={0}
                className={`flex flex-row items-center gap-1 border border-primary-200 px-3 py-1.5 rounded-md text-sm cursor-pointer transition max-w-full truncate ${
                  isSelected
                    ? 'bg-primary-200/10 text-primary-200'
                    : 'text-primary-200 hover:bg-purple-50'
                }`}
              >
                <span className="truncate max-w-full -mt-1 font-semibold">
                  {tag}
                </span>
                {isSelected && (
                  <button
                    type="button"
                    onClick={e => {
                      e.stopPropagation();
                      toggleTag(tag);
                    }}
                  >
                    <X className="h-3.5 w-3.5 text-primary-200 shrink-0" />
                  </button>
                )}
              </div>
            );
          })}
        </div>

        {/* Add more */}
        <Button
          variant="light"
          onPress={() => setShowInput(true)}
          className="flex items-center gap-1 text-sm text-gray-500 hover:text-gray-700 whitespace-nowrap"
        >
          <Plus className="w-3.5 h-3.5" />
          add more
        </Button>
      </div>
        {/* Add more */}
        <Button
          variant="light"
          onPress={() => setShowInput(true)}
          className="flex items-center gap-1 text-sm text-gray-500 hover:text-gray-700 whitespace-nowrap"
        >
          <Plus className="w-3.5 h-3.5" />
          add more
        </Button>
      </div>
    </div>
  );
}
